/**
 * Article utility functions for reading time calculation and date formatting
 */

/**
 * Calculate estimated reading time based on article content
 * Average reading speed: 200 words per minute for Dutch text
 * Minimum: 1 minute
 */
export function calculateReadingTime(content: string): number {
  // Remove markdown syntax and HTML tags for accurate word count
  const cleanContent = content
    .replace(/#{1,6}\s/g, '') // Remove heading syntax
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold syntax
    .replace(/\*(.*?)\*/g, '$1') // Remove italic syntax
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`([^`]+)`/g, '$1') // Remove inline code syntax
    .replace(/---/g, '') // Remove horizontal rules
    .replace(/>\s*/g, '') // Remove blockquote syntax
    .replace(/\|\s*[^|\n]*\s*\|/g, '') // Remove table syntax
    .replace(/-\s*\*\*[^:]+\*\*:/g, '') // Remove bullet point syntax
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  // Count words (Dutch text typically has shorter words than English)
  const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 0).length;
  
  // Calculate minutes (200 words per minute for Dutch)
  const minutes = Math.ceil(wordCount / 200);
  
  // Minimum 1 minute, maximum 20 minutes for display
  return Math.max(1, Math.min(20, minutes));
}

/**
 * Format date to Dutch locale format
 * @param dateString - Date in YYYY-MM-DD format
 * @returns Formatted date like "15 aug 2025"
 */
export function formatDutchDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }

    const months = [
      'jan', 'feb', 'mrt', 'apr', 'mei', 'jun',
      'jul', 'aug', 'sep', 'okt', 'nov', 'dec'
    ];
    
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${month} ${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString; // Return original on error
  }
}

/**
 * Get today's date in YYYY-MM-DD format for new articles
 */
export function getTodayDateString(): string {
  const today = new Date();
  const year = today.getFullYear();
  const month = (today.getMonth() + 1).toString().padStart(2, '0');
  const day = today.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * Calculate how long ago an article was published/updated
 * @param dateString - Date in YYYY-MM-DD format
 * @returns Human readable time ago in Dutch (e.g., "3 dagen geleden")
 */
export function getTimeAgo(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'vandaag';
    } else if (diffInDays === 1) {
      return 'gisteren';
    } else if (diffInDays < 7) {
      return `${diffInDays} dagen geleden`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return weeks === 1 ? '1 week geleden' : `${weeks} weken geleden`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return months === 1 ? '1 maand geleden' : `${months} maanden geleden`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return years === 1 ? '1 jaar geleden' : `${years} jaar geleden`;
    }
  } catch (error) {
    console.error('Error calculating time ago:', error);
    return 'onbekend';
  }
}

/**
 * Validate date format (YYYY-MM-DD)
 */
export function isValidDateFormat(dateString: string): boolean {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}