"use client"

import { motion } from 'framer-motion'
import { <PERSON><PERSON>ir<PERSON>, Quote } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface FactsAndTestimonialsProps {
  className?: string
}

export default function ModernFactsAndTestimonials({ className = "" }: FactsAndTestimonialsProps) {
  const facts = [
    "Nederland ongeveer 1,9 miljoen honden en ruim 3 miljoen katten telt?",
    "naar schatting één op de twintig huisdieren in ons land verzekerd is?",
    "het verzekeren van een huisdier al mogelijk is vanaf zo'n 15 euro per maand?"
  ]

  const testimonials = [
    {
      text: "Na een ongelukje moesten we met onze hond Bram naar de dierenarts, met een gepeperde rekening als gevolg. Gelukkig hadden we via ZoekDierenverzekering.nl een passende verzekering gevonden. Ik had er niet aan moeten denken als we die rekening helemaal zelf hadden moeten betalen...",
      author: "<PERSON><PERSON>"
    },
    {
      text: "Onze hond is inmiddels zo'n vier jaar verzekerd. Gelukkig hebben we de verzekering nog niet hoeven gebruiken voor een grote ingreep. Het geeft mij echter een fijn en gerust gevoel dat hij goed verzekerd is, op het moment dat dit onverhoopt wél nodig is.",
      author: "W. de Jongh"
    }
  ]

  return (
    <section className={`py-8 sm:py-12 ${className}`}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Facts Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
          className="mb-12"
        >
          <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200">
            <CardHeader>
              <CardTitle className="text-2xl sm:text-3xl font-bold text-[#2F2E51] font-figtree">
                Huisdieren in Nederland
              </CardTitle>
              <p className="text-lg text-muted-foreground font-semibold">
                Wist je dat…
              </p>
            </CardHeader>
            <CardContent>
              <ul className="space-y-4">
                {facts.map((fact, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 * index, ease: "easeOut" }}
                    viewport={{ once: true }}
                    className="flex items-start gap-3"
                  >
                    <CheckCircle className="w-5 h-5 text-tea_green-100 mt-0.5 flex-shrink-0" />
                    <span className="text-[#2F2E51] leading-relaxed">
                      …{fact}
                    </span>
                  </motion.li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </motion.div>

        {/* Testimonials Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <div className="mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2F2E51] text-center font-figtree">
              Wat zeggen anderen?
            </h3>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index, ease: "easeOut" }}
                viewport={{ once: true, margin: "-50px" }}
              >
                <Card className="bg-white border-gray-200 hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="pt-6">
                    <div className="relative">
                      <Quote className="w-8 h-8 text-tea_green-100 mb-4 opacity-60" />
                      <blockquote className="text-[#2F2E51] leading-relaxed mb-4">
                        &ldquo;{testimonial.text}&rdquo;
                      </blockquote>
                      <footer className="text-sm text-muted-foreground italic">
                        – {testimonial.author}
                      </footer>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
