{"name": "newproject", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:stable": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report", "test:codegen": "playwright codegen http://localhost:3000"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.6", "@playwright/test": "^1.54.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "gray-matter": "^4.0.3", "lucide-react": "^0.539.0", "next": "15.4.1", "next-mdx-remote": "^5.0.0", "react": "19.1.0", "react-dom": "19.1.0", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}