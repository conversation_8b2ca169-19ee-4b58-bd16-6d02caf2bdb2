"use client"

import { motion } from 'framer-motion'
import { Award, TrendingDown, ChevronDown } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ConclusionSectionProps {
  className?: string
}

export default function ModernConclusionSection({ className = "" }: ConclusionSectionProps) {
  const handleScrollToVerzekeraars = () => {
    const element = document.querySelector('[data-section="top-verzekeraars"]');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  const recommendations = [
    {
      icon: Award,
      title: "Beste voor Katten",
      provider: "Figo Pet",
      price: "~€11/maand",
      description: "Laagste premie voor basisdekking katten",
      color: "from-pink-500 to-rose-500"
    },
    {
      icon: TrendingDown,
      title: "Beste voor Honden", 
      provider: "Figo & OHRA",
      price: "~€17/maand",
      description: "<PERSON><PERSON><PERSON><PERSON> prijzen, verschillende vergoedingspercentages",
      color: "from-blue-500 to-indigo-500"
    }
  ]

  return (
    <section className={`py-8 sm:py-12 lg:py-16 ${className}`}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <Award className="w-4 h-4" />
            <span className="text-sm font-semibold">Onze Conclusie</span>
          </motion.div>
          
          <motion.h2
            className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#2F2E51] mb-4 font-figtree"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            Beste Dierenverzekering 2025
          </motion.h2>
        </motion.div>

        {/* Recommendations Grid */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {recommendations.map((rec, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200">
                <CardHeader>
                  <div className="flex items-center gap-4 mb-2">
                    <div className={`w-12 h-12 bg-gradient-to-br ${rec.color} rounded-xl flex items-center justify-center shadow-lg`}>
                      <rec.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-bold text-[#2F2E51] font-figtree">
                        {rec.title}
                      </CardTitle>
                      <div className="text-2xl font-bold text-green-600">
                        {rec.price}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-3">
                    <div className="text-xl font-semibold text-[#2F2E51] mb-1">
                      {rec.provider}
                    </div>
                    <p className="text-gray-600">
                      {rec.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Key Insight Card */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <Card className="bg-gradient-to-br from-tea_green-50 to-celadon-50 border-tea_green-200">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-tea_green-100 to-celadon-100 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-[#2F2E51] mb-2 font-figtree">
                    Waarom Figo Pet onze voorkeur heeft
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    <strong>Figo Pet</strong> wordt aanbevolen vanwege de uitgebreide dekking en concurrerende prijzen.
                    OHRA biedt een goede prijs-kwaliteit verhouding met 80% vergoeding tegenover Figo&rsquo;s 50%.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <Button
            onClick={handleScrollToVerzekeraars}
            size="lg"
            className="bg-[#2F2E51] hover:bg-[#2F2E51]/90 text-white font-semibold px-8 py-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            Bekijk Alle Verzekeraars
            <ChevronDown className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
